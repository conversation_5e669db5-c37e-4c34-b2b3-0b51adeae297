"use client";

import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { AudioLines, Upload, X, FileAudio } from "lucide-react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { useState, useEffect, useRef } from "react";
import { toast } from "sonner";
import { useUnifiedFileUpload } from "@/hooks/global/use-unified-file-upload";
import { Progress } from "@/components/ui/progress";
import { env } from "@/env";

interface VoiceDubbingSettingsProps {
  enabled: boolean;
  service: number;
  voiceName: string;
  cloneVoiceFileUrl?: string;
  onEnabledChange: (enabled: boolean) => void;
  onServiceChange: (service: number) => void;
  onVoiceNameChange: (voiceName: string) => void;
  onCloneVoiceFileUrlChange: (fileUrl: string | undefined) => void;
}

interface Voice {
  name: string;
  language?: string;
  gender?: string;
}

// TTS Service mapping
const TTS_SERVICES = [
  { value: 0, label: "Edge-TTS" },
  { value: 1, label: "CosyVoice" },
  { value: 2, label: "ChatTTS" },
  { value: 3, label: "302.AI" },
  { value: 4, label: "FishTTS" },
  { value: 5, label: "Azure-TTS" },
  { value: 6, label: "GPT-SoVITS" },
  { value: 7, label: "clone-voice" },
  { value: 8, label: "OpenAI TTS" },
  { value: 9, label: "Elevenlabs.io" },
  { value: 10, label: "Google TTS" },
  { value: 11, label: "Custom TTS API" },
  { value: 12, label: "VolcEngine TTS" },
  { value: 13, label: "F5-TTS" },
  { value: 14, label: "Kokoro TTS" },
  { value: 15, label: "Google Cloud TTS" },
  { value: 16, label: "Gemini TTS" },
  { value: 17, label: "AuSyncLab" },
];

export const VoiceDubbingSettings = ({
  enabled,
  service,
  voiceName,
  cloneVoiceFileUrl,
  onEnabledChange,
  onServiceChange,
  onVoiceNameChange,
  onCloneVoiceFileUrlChange,
}: VoiceDubbingSettingsProps) => {
  const t = useTranslations();
  const [voices, setVoices] = useState<Voice[]>([]);
  const [isLoadingVoices, setIsLoadingVoices] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // File upload hook for clone voice
  const {
    handleFileSelect,
    selectedFiles,
    uploadProgress,
    isUploading,
    error: uploadError,
    upload,
    removeFile,
  } = useUnifiedFileUpload({
    validationConfig: {
      maxFileSize: 50 * 1024 * 1024, // 50MB
      acceptedTypes: {
        "audio/": ["mp3", "wav", "m4a", "aac", "ogg", "flac"],
      },
      maxFiles: 1,
    },
    onUploadSuccess: (files) => {
      if (files.length > 0) {
        onCloneVoiceFileUrlChange(files[0].url);
        toast.success("Voice file uploaded successfully!");
      }
    },
    onUploadError: (error) => {
      toast.error(`Failed to upload voice file: ${error.message}`);
    },
  });

  // Fetch voices when service changes
  useEffect(() => {
    if (enabled && service !== undefined) {
      fetchVoices(service);
    }
  }, [enabled, service]);

  const fetchVoices = async (ttsType: number) => {
    setIsLoadingVoices(true);
    try {
      const apiHost = env.NEXT_PUBLIC_BASE_API_HOST || 'http://127.0.0.1:9011';
      const response = await fetch(`${apiHost}/voices?tts_type=${ttsType}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch voices: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('[VOICE_DUBBING] Fetched voices for service', ttsType, ':', data);

      // Check for API error response
      if (data.code !== 0) {
        throw new Error(data.msg || `API returned error code: ${data.code}`);
      }

      // Handle different response formats
      let voiceList: Voice[] = [];
      if (Array.isArray(data)) {
        voiceList = data.map(voice => ({ name: voice }));
      } else if (data.data && data.data.voices && Array.isArray(data.data.voices)) {
        // Handle the actual API response format: {code: 0, data: {voices: [...]}, msg: "ok"}
        voiceList = data.data.voices
          .filter((voice: string) => voice !== "No") // Filter out "No" option
          .map((voice: string) => ({ name: voice }));
      } else if (data.voices && Array.isArray(data.voices)) {
        voiceList = data.voices.map(voice => ({ name: voice }));
      } else {
        console.warn('[VOICE_DUBBING] Unexpected response format:', data);
        voiceList = [];
      }

      setVoices(voiceList);

      // If current voice is not in the new list, reset it
      if (voiceName && !voiceList.some(voice => voice.name === voiceName)) {
        onVoiceNameChange("");
      }

      // Auto-select first voice if none selected
      if (!voiceName && voiceList.length > 0) {
        onVoiceNameChange(voiceList[0].name);
      }

    } catch (error) {
      console.error('[VOICE_DUBBING] Error fetching voices:', error);
      toast.error(`Failed to load voices: ${error instanceof Error ? error.message : String(error)}`);
      setVoices([]);
    } finally {
      setIsLoadingVoices(false);
    }
  };

  const handleServiceChange = (newService: string) => {
    const serviceNumber = parseInt(newService, 10);
    onServiceChange(serviceNumber);
    // Reset voice selection when service changes
    onVoiceNameChange("");
  };

  const selectedService = TTS_SERVICES.find(s => s.value === service);

  return (
    <div className="space-y-3">
      {/* Voice Dubbing Toggle */}
      <div className="flex items-center justify-between rounded-md border bg-card/50 px-2.5 py-1.5">
        <div className="flex items-center gap-1.5">
          <AudioLines className="size-3.5 text-muted-foreground" />
          <span className="text-xs">Voice Dubbing</span>
        </div>
        <div className="flex items-center gap-2">
          <Label className="text-[0.7rem] text-muted-foreground">
            {enabled ? t("common.enabled") : t("common.disabled")}
          </Label>
          <Switch
            checked={enabled}
            onCheckedChange={onEnabledChange}
          />
        </div>
      </div>

      {/* Service and Voice Selection - only show when enabled */}
      {enabled && (
        <div className="space-y-2 pl-4 border-l-2 border-muted">
          {/* Service Selection */}
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">Dubbing Service</Label>
            <Select value={service.toString()} onValueChange={handleServiceChange}>
              <SelectTrigger className="h-7 text-xs">
                <SelectValue placeholder="Select service" />
              </SelectTrigger>
              <SelectContent>
                {TTS_SERVICES.map((serviceOption) => (
                  <SelectItem
                    key={serviceOption.value}
                    value={serviceOption.value.toString()}
                    className="text-xs"
                  >
                    {serviceOption.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Voice Selection */}
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">
              Voice Name
              {selectedService && (
                <span className="ml-1 text-[0.65rem] opacity-70">
                  ({selectedService.label})
                </span>
              )}
            </Label>
            <Select 
              value={voiceName} 
              onValueChange={onVoiceNameChange}
              disabled={isLoadingVoices || voices.length === 0}
            >
              <SelectTrigger className="h-7 text-xs">
                <SelectValue 
                  placeholder={
                    isLoadingVoices 
                      ? "Loading voices..." 
                      : voices.length === 0 
                        ? "No voices available" 
                        : "Select voice"
                  } 
                />
              </SelectTrigger>
              <SelectContent>
                {voices.map((voice, index) => (
                  <SelectItem
                    key={`${voice.name}-${index}`}
                    value={voice.name}
                    className="text-xs"
                  >
                    <div className="flex flex-col">
                      <span>{voice.name}</span>
                      {(voice.language || voice.gender) && (
                        <span className="text-[0.65rem] text-muted-foreground">
                          {[voice.language, voice.gender].filter(Boolean).join(" • ")}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Clone Voice File Upload - only show when voice name is "clone" */}
          {voiceName === "clone" && (
            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">
                Clone Voice File
                <span className="ml-1 text-[0.65rem] opacity-70">
                  (Upload audio file for voice cloning)
                </span>
              </Label>

              {/* File Upload Area */}
              <div className="space-y-2">
                {!cloneVoiceFileUrl && selectedFiles.length === 0 && (
                  <div className="relative">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="audio/*"
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      onChange={(e) => {
                        const files = e.target.files;
                        if (files && files.length > 0) {
                          handleFileSelect(Array.from(files));
                          upload(Array.from(files));
                        }
                      }}
                    />
                    <div className="flex items-center justify-center gap-2 rounded-md border border-dashed border-muted-foreground/25 bg-muted/10 px-3 py-2 text-xs text-muted-foreground hover:bg-muted/20 transition-colors">
                      <Upload className="size-3" />
                      <span>Click to upload audio file</span>
                    </div>
                  </div>
                )}

                {/* Selected File Display */}
                {selectedFiles.length > 0 && (
                  <div className="flex items-center justify-between gap-2 rounded-md border bg-muted/10 px-2 py-1.5">
                    <div className="flex items-center gap-1.5 flex-1 min-w-0">
                      <FileAudio className="size-3 shrink-0 text-muted-foreground" />
                      <span className="text-xs truncate">{selectedFiles[0].name}</span>
                    </div>
                    {!isUploading && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="size-5 shrink-0"
                        onClick={() => {
                          removeFile(0);
                          onCloneVoiceFileUrlChange(undefined);
                        }}
                      >
                        <X className="size-3" />
                      </Button>
                    )}
                  </div>
                )}

                {/* Upload Progress */}
                {isUploading && (
                  <div className="space-y-1">
                    <Progress value={uploadProgress} className="h-1" />
                    <div className="text-xs text-muted-foreground">
                      Uploading... {Math.round(uploadProgress)}%
                    </div>
                  </div>
                )}

                {/* Upload Error */}
                {uploadError && (
                  <div className="text-xs text-destructive">
                    {uploadError}
                  </div>
                )}

                {/* Uploaded File Info */}
                {cloneVoiceFileUrl && !isUploading && (
                  <div className="flex items-center justify-between gap-2 rounded-md border bg-green-50 dark:bg-green-950/20 px-2 py-1.5">
                    <div className="flex items-center gap-1.5 flex-1 min-w-0">
                      <FileAudio className="size-3 shrink-0 text-green-600 dark:text-green-400" />
                      <span className="text-xs text-green-700 dark:text-green-300">
                        Voice file uploaded successfully
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-5 shrink-0"
                      onClick={() => {
                        onCloneVoiceFileUrlChange(undefined);
                        if (fileInputRef.current) {
                          fileInputRef.current.value = '';
                        }
                      }}
                    >
                      <X className="size-3" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Status/Info */}
          {isLoadingVoices && (
            <div className="text-xs text-muted-foreground">
              Loading available voices...
            </div>
          )}
          {!isLoadingVoices && voices.length === 0 && (
            <div className="text-xs text-muted-foreground">
              No voices available for selected service
            </div>
          )}
          {!isLoadingVoices && voices.length > 0 && (
            <div className="text-xs text-muted-foreground">
              {voices.length} voice{voices.length !== 1 ? 's' : ''} available
            </div>
          )}
        </div>
      )}
    </div>
  );
};
